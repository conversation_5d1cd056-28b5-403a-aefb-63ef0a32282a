body, html, #root, .app {
  height: 100%;
  margin: 0;
  padding: 0;
  font-family: 'Comic Sans MS', 'PingFang SC', 'Arial', sans-serif;
  background: #fffbe6;
}

.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  transition: background 0.5s;
}

.title {
  margin-top: 32px;
  font-size: 2.2rem;
  color: #ffb703;
  letter-spacing: 2px;
  text-shadow: 1px 2px 8px #fffbe6;
}

.mood-btns {
  display: flex;
  gap: 24px;
  margin: 40px 0 32px 0;
}

.mood-btn {
  border: none;
  border-radius: 18px;
  padding: 24px 18px 16px 18px;
  font-size: 1.2rem;
  box-shadow: 0 2px 12px #ffe06655;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: transform 0.15s, box-shadow 0.2s;
  position: relative;
  min-width: 80px;
  min-height: 80px;
}
.mood-btn:hover {
  transform: scale(1.08) rotate(-2deg);
  box-shadow: 0 4px 24px #ffd6e055;
}
.emoji {
  font-size: 2.2rem;
  margin-bottom: 6px;
}
.label {
  font-size: 1.1rem;
  color: #333;
}
.count {
  font-size: 1.1rem;
  color: #ffb703;
  margin-top: 4px;
  font-weight: bold;
}

.floating-emoji {
  position: fixed;
  left: 50%;
  top: 60%;
  font-size: 3.5rem;
  pointer-events: none;
  animation: floatUp 1.2s cubic-bezier(.4,1.6,.6,1) forwards;
  z-index: 100;
}
@keyframes floatUp {
  0% { opacity: 0; transform: translate(-50%, 0) scale(0.7); }
  20% { opacity: 1; transform: translate(-50%, -20px) scale(1.1); }
  80% { opacity: 1; transform: translate(-50%, -80px) scale(1.1); }
  100% { opacity: 0; transform: translate(-50%, -120px) scale(0.7); }
}

.footer {
  margin-top: 40px;
  color: #bdb2ff;
  font-size: 1.1rem;
  letter-spacing: 1px;
  text-shadow: 1px 1px 6px #fff;
} 