body, html, #root, .app {
  height: 100%;
  margin: 0;
  padding: 0;
  font-family: 'Comic Sans MS', 'PingFang SC', 'Arial', sans-serif;
  background: #fffbe6;
}

.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  transition: background 0.5s;
}

header {
  text-align: center;
  margin-bottom: 20px;
}

.title {
  margin-top: 32px;
  margin-bottom: 0;
  font-size: 2.2rem;
  color: #ffb703;
  letter-spacing: 2px;
  text-shadow: 1px 2px 8px #fffbe6;
}

main {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
}

section {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
}

.action-buttons {
  flex-direction: row !important;
  gap: 10px;
  justify-content: center;
  flex-wrap: wrap;
}

.mood-btns {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 20px;
  margin: 40px 20px 32px 20px;
  max-width: 800px;
  width: 100%;
}

.mood-btn {
  border: none;
  border-radius: 18px;
  padding: 24px 18px 16px 18px;
  font-size: 1.2rem;
  box-shadow: 0 2px 12px #ffe06655;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  min-width: 80px;
  min-height: 80px;
  overflow: hidden;
}

.mood-btn::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: all 0.6s ease;
}

.mood-btn:hover {
  transform: scale(1.08) rotate(-2deg);
  box-shadow: 0 8px 32px #ffd6e055;
}

.mood-btn:active {
  transform: scale(0.95) rotate(2deg);
  box-shadow: 0 2px 8px #ffe06655;
}

.mood-btn:active::before {
  width: 300px;
  height: 300px;
}

.mood-btn.clicked {
  animation: buttonPulse 0.6s ease-out;
}

@keyframes buttonPulse {
  0% {
    transform: scale(1) rotate(0deg);
    box-shadow: 0 2px 12px #ffe06655;
  }
  25% {
    transform: scale(1.15) rotate(-5deg);
    box-shadow: 0 8px 24px #ffd6e088;
  }
  50% {
    transform: scale(1.1) rotate(3deg);
    box-shadow: 0 12px 36px #ffd6e0aa;
  }
  75% {
    transform: scale(1.05) rotate(-2deg);
    box-shadow: 0 8px 24px #ffd6e088;
  }
  100% {
    transform: scale(1) rotate(0deg);
    box-shadow: 0 2px 12px #ffe06655;
  }
}
.emoji {
  font-size: 2.2rem;
  margin-bottom: 6px;
}
.label {
  font-size: 1.1rem;
  color: #333;
}
.count {
  font-size: 1.1rem;
  color: #ffb703;
  margin-top: 4px;
  font-weight: bold;
}

.floating-emoji {
  position: fixed;
  left: 50%;
  top: 60%;
  font-size: 3.5rem;
  pointer-events: none;
  animation: floatUp 1.2s cubic-bezier(.4,1.6,.6,1) forwards;
  z-index: 100;
}
@keyframes floatUp {
  0% { opacity: 0; transform: translate(-50%, 0) scale(0.7); }
  20% { opacity: 1; transform: translate(-50%, -20px) scale(1.1); }
  80% { opacity: 1; transform: translate(-50%, -80px) scale(1.1); }
  100% { opacity: 0; transform: translate(-50%, -120px) scale(0.7); }
}

.sound-controls {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 20px;
  padding: 12px 20px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 25px;
  backdrop-filter: blur(10px);
}

.sound-toggle {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.sound-toggle:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

.volume-slider {
  width: 100px;
  height: 6px;
  border-radius: 3px;
  background: rgba(255, 255, 255, 0.3);
  outline: none;
  cursor: pointer;
  -webkit-appearance: none;
}

.volume-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: #ffb703;
  cursor: pointer;
  box-shadow: 0 2px 6px rgba(0,0,0,0.2);
  transition: all 0.3s ease;
}

.volume-slider::-webkit-slider-thumb:hover {
  transform: scale(1.2);
  box-shadow: 0 4px 12px rgba(0,0,0,0.3);
}

.footer {
  margin-top: 40px;
  color: #bdb2ff;
  font-size: 1.1rem;
  letter-spacing: 1px;
  text-shadow: 1px 1px 6px #fff;
}

/* 响应式设计 */
@media (max-width: 768px) {
  header {
    margin-bottom: 15px;
  }

  .title {
    font-size: 1.8rem;
    margin-top: 20px;
  }

  .mood-btns {
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
    margin: 30px 15px 25px 15px;
  }

  .mood-btn {
    min-width: 70px;
    min-height: 70px;
    padding: 20px 15px 14px 15px;
  }

  .emoji {
    font-size: 1.8rem;
  }

  .label {
    font-size: 1rem;
  }

  .count {
    font-size: 1rem;
  }

  .sound-controls {
    margin-bottom: 15px;
    padding: 10px 16px;
  }

  .volume-slider {
    width: 80px;
  }

  .floating-emoji {
    font-size: 2.5rem;
  }
}

@media (max-width: 480px) {
  header {
    margin-bottom: 10px;
  }

  .title {
    font-size: 1.5rem;
    margin-top: 15px;
  }

  .mood-btns {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
    margin: 25px 10px 20px 10px;
  }

  .mood-btn {
    min-width: 60px;
    min-height: 60px;
    padding: 16px 12px 10px 12px;
  }

  .emoji {
    font-size: 1.5rem;
    margin-bottom: 4px;
  }

  .label {
    font-size: 0.9rem;
  }

  .count {
    font-size: 0.9rem;
  }

  .sound-controls {
    padding: 8px 12px;
  }

  .sound-toggle {
    font-size: 1.2rem;
    padding: 6px;
  }

  .volume-slider {
    width: 60px;
  }

  .floating-emoji {
    font-size: 2rem;
  }

  .footer {
    font-size: 1rem;
    margin-top: 30px;
  }
}

/* 心情统计样式 */
.mood-stats {
  width: 100%;
  max-width: 800px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 16px;
  padding: 20px;
  margin: 20px 0;
  backdrop-filter: blur(10px);
}

.stats-title {
  text-align: center;
  color: #ffb703;
  margin-bottom: 20px;
  font-size: 1.3rem;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 16px;
  margin-bottom: 20px;
}

.stat-card {
  background: rgba(255, 255, 255, 0.4);
  border-radius: 12px;
  padding: 16px;
  text-align: center;
  transition: transform 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
}

.stat-number {
  font-size: 2rem;
  font-weight: bold;
  color: #ffb703;
  margin-bottom: 8px;
}

.stat-emoji {
  font-size: 2.5rem;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 0.9rem;
  color: #666;
  font-weight: 500;
}

.stat-sublabel {
  font-size: 1rem;
  color: #ffb703;
  font-weight: bold;
  margin-top: 4px;
}

.mood-analysis {
  background: rgba(255, 183, 3, 0.1);
  border-radius: 12px;
  padding: 16px;
  margin: 20px 0;
  border-left: 4px solid #ffb703;
}

.analysis-text {
  margin: 0;
  color: #333;
  font-size: 1rem;
  line-height: 1.5;
  text-align: center;
}

.mood-breakdown {
  margin-top: 20px;
}

.mood-breakdown h4 {
  color: #ffb703;
  margin-bottom: 12px;
  font-size: 1.1rem;
}

.breakdown-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  margin-bottom: 8px;
  transition: background 0.3s ease;
}

.breakdown-item:hover {
  background: rgba(255, 255, 255, 0.3);
}

.breakdown-emoji {
  font-size: 1.2rem;
  width: 24px;
  text-align: center;
}

.breakdown-label {
  flex: 1;
  color: #333;
  font-weight: 500;
}

.breakdown-count {
  color: #ffb703;
  font-weight: bold;
  margin-right: 8px;
}

.breakdown-percentage {
  color: #666;
  font-size: 0.9rem;
}

/* 移动端优化 */
@media (max-width: 768px) {
  .mood-stats {
    margin: 15px 10px;
    padding: 16px;
  }

  .stats-grid {
    grid-template-columns: 1fr 1fr;
    gap: 12px;
  }

  .stat-card {
    padding: 12px;
  }

  .stat-number {
    font-size: 1.5rem;
  }

  .stat-emoji {
    font-size: 2rem;
  }

  .breakdown-item {
    gap: 8px;
    padding: 6px 10px;
  }
}

/* 分享功能样式 */
.share-btn {
  background: linear-gradient(135deg, #ffb703, #ffd60a);
  border: none;
  border-radius: 25px;
  padding: 12px 24px;
  color: white;
  font-size: 1rem;
  font-weight: bold;
  cursor: pointer;
  margin: 20px 0;
  box-shadow: 0 4px 15px rgba(255, 183, 3, 0.3);
  transition: all 0.3s ease;
}

.share-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(255, 183, 3, 0.4);
}

.share-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.share-card {
  background: white;
  border-radius: 20px;
  max-width: 400px;
  width: 100%;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.share-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 20px 0 20px;
  border-bottom: 1px solid #f0f0f0;
}

.share-header h3 {
  margin: 0;
  color: #ffb703;
  font-size: 1.3rem;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #999;
  padding: 5px;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.close-btn:hover {
  background: #f0f0f0;
  color: #666;
}

.share-content {
  padding: 20px;
}

.share-stats {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
  justify-content: center;
}

.share-total {
  text-align: center;
}

.big-number {
  display: block;
  font-size: 2.5rem;
  font-weight: bold;
  color: #ffb703;
  line-height: 1;
}

.share-total .label {
  font-size: 0.9rem;
  color: #666;
}

.share-mood {
  text-align: center;
}

.mood-emoji {
  display: block;
  font-size: 2.5rem;
  margin-bottom: 5px;
}

.mood-label {
  display: block;
  font-weight: bold;
  color: #333;
  margin-bottom: 2px;
}

.mood-subtitle {
  font-size: 0.9rem;
  color: #666;
}

.share-text {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 16px;
  margin: 20px 0;
  white-space: pre-line;
  font-size: 0.95rem;
  line-height: 1.5;
  color: #333;
  border-left: 4px solid #ffb703;
}

.share-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.copy-btn {
  background: #ffb703;
  border: none;
  border-radius: 8px;
  padding: 10px 20px;
  color: white;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
}

.copy-btn:hover {
  background: #e6a503;
}

.cancel-btn {
  background: #f0f0f0;
  border: none;
  border-radius: 8px;
  padding: 10px 20px;
  color: #666;
  cursor: pointer;
  transition: all 0.3s ease;
}

.cancel-btn:hover {
  background: #e0e0e0;
}

/* 移动端优化 */
@media (max-width: 768px) {
  .share-modal {
    padding: 10px;
  }

  .share-card {
    max-width: 100%;
  }

  .share-stats {
    flex-direction: column;
    gap: 15px;
  }

  .share-actions {
    flex-direction: column;
  }

  .copy-btn, .cancel-btn {
    width: 100%;
  }
}

/* 心情历史样式 */
.history-btn {
  background: linear-gradient(135deg, #bdb2ff, #c2f0c2);
  border: none;
  border-radius: 25px;
  padding: 12px 24px;
  color: white;
  font-size: 1rem;
  font-weight: bold;
  cursor: pointer;
  margin: 10px;
  box-shadow: 0 4px 15px rgba(189, 178, 255, 0.3);
  transition: all 0.3s ease;
}

.history-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(189, 178, 255, 0.4);
}

.history-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.history-panel {
  background: white;
  border-radius: 20px;
  max-width: 500px;
  width: 100%;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 20px 0 20px;
  border-bottom: 1px solid #f0f0f0;
}

.history-header h3 {
  margin: 0;
  color: #bdb2ff;
  font-size: 1.3rem;
}

.history-filters {
  padding: 15px 20px;
  border-bottom: 1px solid #f0f0f0;
}

.date-filter {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 1rem;
  background: white;
  cursor: pointer;
}

.history-content {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
}

.empty-history {
  text-align: center;
  color: #666;
  padding: 40px 20px;
}

.empty-history p {
  margin: 10px 0;
}

.history-day {
  margin-bottom: 20px;
  border-radius: 12px;
  background: rgba(189, 178, 255, 0.05);
  overflow: hidden;
}

.day-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: rgba(189, 178, 255, 0.1);
  border-bottom: 1px solid rgba(189, 178, 255, 0.2);
}

.day-date {
  font-weight: bold;
  color: #333;
  font-size: 1.1rem;
}

.day-summary {
  font-size: 0.9rem;
  color: #666;
  background: rgba(255, 255, 255, 0.7);
  padding: 4px 8px;
  border-radius: 12px;
}

.day-entries {
  padding: 12px 16px;
}

.history-entry {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
  padding: 8px 0;
}

.entry-time {
  font-size: 0.9rem;
  color: #666;
  min-width: 60px;
  font-family: monospace;
}

.entry-mood {
  padding: 6px 12px;
  border-radius: 15px;
  font-size: 0.9rem;
  font-weight: 500;
  color: #333;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 移动端优化 */
@media (max-width: 768px) {
  .history-modal {
    padding: 10px;
  }

  .history-panel {
    max-width: 100%;
    max-height: 90vh;
  }

  .day-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .day-summary {
    align-self: flex-end;
  }

  .history-entry {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .entry-time {
    min-width: auto;
    font-size: 0.8rem;
  }
}