body, html, #root, .app {
  height: 100%;
  margin: 0;
  padding: 0;
  font-family: 'Comic Sans MS', 'PingFang SC', 'Arial', sans-serif;
  background: #fffbe6;
}

.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  transition: background 0.5s;
}

.title {
  margin-top: 32px;
  font-size: 2.2rem;
  color: #ffb703;
  letter-spacing: 2px;
  text-shadow: 1px 2px 8px #fffbe6;
}

.mood-btns {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 20px;
  margin: 40px 20px 32px 20px;
  max-width: 800px;
  width: 100%;
}

.mood-btn {
  border: none;
  border-radius: 18px;
  padding: 24px 18px 16px 18px;
  font-size: 1.2rem;
  box-shadow: 0 2px 12px #ffe06655;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  min-width: 80px;
  min-height: 80px;
  overflow: hidden;
}

.mood-btn::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: all 0.6s ease;
}

.mood-btn:hover {
  transform: scale(1.08) rotate(-2deg);
  box-shadow: 0 8px 32px #ffd6e055;
}

.mood-btn:active {
  transform: scale(0.95) rotate(2deg);
  box-shadow: 0 2px 8px #ffe06655;
}

.mood-btn:active::before {
  width: 300px;
  height: 300px;
}

.mood-btn.clicked {
  animation: buttonPulse 0.6s ease-out;
}

@keyframes buttonPulse {
  0% {
    transform: scale(1) rotate(0deg);
    box-shadow: 0 2px 12px #ffe06655;
  }
  25% {
    transform: scale(1.15) rotate(-5deg);
    box-shadow: 0 8px 24px #ffd6e088;
  }
  50% {
    transform: scale(1.1) rotate(3deg);
    box-shadow: 0 12px 36px #ffd6e0aa;
  }
  75% {
    transform: scale(1.05) rotate(-2deg);
    box-shadow: 0 8px 24px #ffd6e088;
  }
  100% {
    transform: scale(1) rotate(0deg);
    box-shadow: 0 2px 12px #ffe06655;
  }
}
.emoji {
  font-size: 2.2rem;
  margin-bottom: 6px;
}
.label {
  font-size: 1.1rem;
  color: #333;
}
.count {
  font-size: 1.1rem;
  color: #ffb703;
  margin-top: 4px;
  font-weight: bold;
}

.floating-emoji {
  position: fixed;
  left: 50%;
  top: 60%;
  font-size: 3.5rem;
  pointer-events: none;
  animation: floatUp 1.2s cubic-bezier(.4,1.6,.6,1) forwards;
  z-index: 100;
}
@keyframes floatUp {
  0% { opacity: 0; transform: translate(-50%, 0) scale(0.7); }
  20% { opacity: 1; transform: translate(-50%, -20px) scale(1.1); }
  80% { opacity: 1; transform: translate(-50%, -80px) scale(1.1); }
  100% { opacity: 0; transform: translate(-50%, -120px) scale(0.7); }
}

.sound-controls {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 20px;
  padding: 12px 20px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 25px;
  backdrop-filter: blur(10px);
}

.sound-toggle {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.sound-toggle:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

.volume-slider {
  width: 100px;
  height: 6px;
  border-radius: 3px;
  background: rgba(255, 255, 255, 0.3);
  outline: none;
  cursor: pointer;
  -webkit-appearance: none;
}

.volume-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: #ffb703;
  cursor: pointer;
  box-shadow: 0 2px 6px rgba(0,0,0,0.2);
  transition: all 0.3s ease;
}

.volume-slider::-webkit-slider-thumb:hover {
  transform: scale(1.2);
  box-shadow: 0 4px 12px rgba(0,0,0,0.3);
}

.footer {
  margin-top: 40px;
  color: #bdb2ff;
  font-size: 1.1rem;
  letter-spacing: 1px;
  text-shadow: 1px 1px 6px #fff;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .title {
    font-size: 1.8rem;
    margin-top: 20px;
  }

  .mood-btns {
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
    margin: 30px 15px 25px 15px;
  }

  .mood-btn {
    min-width: 70px;
    min-height: 70px;
    padding: 20px 15px 14px 15px;
  }

  .emoji {
    font-size: 1.8rem;
  }

  .label {
    font-size: 1rem;
  }

  .count {
    font-size: 1rem;
  }

  .sound-controls {
    margin-bottom: 15px;
    padding: 10px 16px;
  }

  .volume-slider {
    width: 80px;
  }

  .floating-emoji {
    font-size: 2.5rem;
  }
}

@media (max-width: 480px) {
  .title {
    font-size: 1.5rem;
    margin-top: 15px;
  }

  .mood-btns {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
    margin: 25px 10px 20px 10px;
  }

  .mood-btn {
    min-width: 60px;
    min-height: 60px;
    padding: 16px 12px 10px 12px;
  }

  .emoji {
    font-size: 1.5rem;
    margin-bottom: 4px;
  }

  .label {
    font-size: 0.9rem;
  }

  .count {
    font-size: 0.9rem;
  }

  .sound-controls {
    padding: 8px 12px;
  }

  .sound-toggle {
    font-size: 1.2rem;
    padding: 6px;
  }

  .volume-slider {
    width: 60px;
  }

  .floating-emoji {
    font-size: 2rem;
  }

  .footer {
    font-size: 1rem;
    margin-top: 30px;
  }
}

/* 心情统计样式 */
.mood-stats {
  width: 100%;
  max-width: 800px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 16px;
  padding: 20px;
  margin: 20px 0;
  backdrop-filter: blur(10px);
}

.stats-title {
  text-align: center;
  color: #ffb703;
  margin-bottom: 20px;
  font-size: 1.3rem;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 16px;
  margin-bottom: 20px;
}

.stat-card {
  background: rgba(255, 255, 255, 0.4);
  border-radius: 12px;
  padding: 16px;
  text-align: center;
  transition: transform 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
}

.stat-number {
  font-size: 2rem;
  font-weight: bold;
  color: #ffb703;
  margin-bottom: 8px;
}

.stat-emoji {
  font-size: 2.5rem;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 0.9rem;
  color: #666;
  font-weight: 500;
}

.stat-sublabel {
  font-size: 1rem;
  color: #ffb703;
  font-weight: bold;
  margin-top: 4px;
}

.mood-analysis {
  background: rgba(255, 183, 3, 0.1);
  border-radius: 12px;
  padding: 16px;
  margin: 20px 0;
  border-left: 4px solid #ffb703;
}

.analysis-text {
  margin: 0;
  color: #333;
  font-size: 1rem;
  line-height: 1.5;
  text-align: center;
}

.mood-breakdown {
  margin-top: 20px;
}

.mood-breakdown h4 {
  color: #ffb703;
  margin-bottom: 12px;
  font-size: 1.1rem;
}

.breakdown-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  margin-bottom: 8px;
  transition: background 0.3s ease;
}

.breakdown-item:hover {
  background: rgba(255, 255, 255, 0.3);
}

.breakdown-emoji {
  font-size: 1.2rem;
  width: 24px;
  text-align: center;
}

.breakdown-label {
  flex: 1;
  color: #333;
  font-weight: 500;
}

.breakdown-count {
  color: #ffb703;
  font-weight: bold;
  margin-right: 8px;
}

.breakdown-percentage {
  color: #666;
  font-size: 0.9rem;
}

/* 移动端优化 */
@media (max-width: 768px) {
  .mood-stats {
    margin: 15px 10px;
    padding: 16px;
  }

  .stats-grid {
    grid-template-columns: 1fr 1fr;
    gap: 12px;
  }

  .stat-card {
    padding: 12px;
  }

  .stat-number {
    font-size: 1.5rem;
  }

  .stat-emoji {
    font-size: 2rem;
  }

  .breakdown-item {
    gap: 8px;
    padding: 6px 10px;
  }
}