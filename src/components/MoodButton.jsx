import { useState } from "react";

export default function MoodButton({ mood, count, onClick }) {
  const [isClicked, setIsClicked] = useState(false);

  const handleClick = () => {
    setIsClicked(true);
    onClick(mood);

    // 重置动画状态
    setTimeout(() => setIsClicked(false), 600);
  };

  return (
    <button
      className={`mood-btn ${isClicked ? 'clicked' : ''}`}
      style={{ background: mood.color }}
      onClick={handleClick}
    >
      <span className="emoji">{mood.emoji}</span>
      <span className="label">{mood.label}</span>
      <span className="count">{count}</span>
    </button>
  );
}