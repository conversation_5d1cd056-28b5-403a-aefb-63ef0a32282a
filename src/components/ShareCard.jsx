import { useState } from "react";

export default function ShareCard({ data }) {
  const [showShareCard, setShowShareCard] = useState(false);
  
  const totalClicks = data.reduce((sum, mood) => sum + mood.count, 0);
  const mostCommonMood = data.reduce((max, mood) => 
    mood.count > max.count ? mood : max, 
    { count: 0, label: "暂无", emoji: "😊" }
  );

  const generateShareText = () => {
    if (totalClicks === 0) {
      return "我在心泡泡开始记录心情啦！🌟 一起来记录每一天的美好心情吧～";
    }
    
    const topMoods = data
      .filter(mood => mood.count > 0)
      .sort((a, b) => b.count - a.count)
      .slice(0, 3);
    
    let shareText = `我在心泡泡记录了 ${totalClicks} 次心情！\n\n`;
    shareText += `最常见的心情是：${mostCommonMood.emoji} ${mostCommonMood.label}\n\n`;
    
    if (topMoods.length > 1) {
      shareText += "我的心情分布：\n";
      topMoods.forEach((mood, index) => {
        const emoji = data.find(m => m.label === mood.label)?.emoji || "😊";
        shareText += `${index + 1}. ${emoji} ${mood.label}: ${mood.count}次\n`;
      });
    }
    
    shareText += "\n一起来记录心情，治愈每一天 💖";
    return shareText;
  };

  const handleShare = async () => {
    const shareText = generateShareText();
    
    if (navigator.share) {
      try {
        await navigator.share({
          title: '心泡泡',
          text: shareText,
          url: window.location.href
        });
      } catch (err) {
        console.log('分享取消或失败');
      }
    } else {
      // 降级到复制到剪贴板
      try {
        await navigator.clipboard.writeText(shareText);
        alert('心情卡片已复制到剪贴板！');
      } catch (err) {
        // 如果剪贴板API也不支持，显示分享卡片
        setShowShareCard(true);
      }
    }
  };

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(generateShareText());
      alert('已复制到剪贴板！');
      setShowShareCard(false);
    } catch (err) {
      console.error('复制失败:', err);
    }
  };

  return (
    <>
      <button className="share-btn" onClick={handleShare}>
        分享心情 📤
      </button>
      
      {showShareCard && (
        <div className="share-modal">
          <div className="share-card">
            <div className="share-header">
              <h3>我的心情卡片</h3>
              <button 
                className="close-btn"
                onClick={() => setShowShareCard(false)}
              >
                ✕
              </button>
            </div>
            
            <div className="share-content">
              <div className="share-stats">
                <div className="share-total">
                  <span className="big-number">{totalClicks}</span>
                  <span className="label">次心情记录</span>
                </div>
                
                {mostCommonMood.count > 0 && (
                  <div className="share-mood">
                    <span className="mood-emoji">{mostCommonMood.emoji}</span>
                    <span className="mood-label">{mostCommonMood.label}</span>
                    <span className="mood-subtitle">最常见心情</span>
                  </div>
                )}
              </div>
              
              <div className="share-text">
                {generateShareText()}
              </div>
              
              <div className="share-actions">
                <button className="copy-btn" onClick={copyToClipboard}>
                  复制文本
                </button>
                <button 
                  className="cancel-btn"
                  onClick={() => setShowShareCard(false)}
                >
                  取消
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
