import { <PERSON>, <PERSON><PERSON><PERSON>, Cell, ResponsiveContainer, <PERSON><PERSON><PERSON>, <PERSON>A<PERSON><PERSON>, YAxis } from "recharts";

// 为每个心情定义渐变色
const moodColors = {
  "开心": "#ffe066",
  "不开心": "#a0c4ff",
  "超开心": "#ffd6e0",
  "平静": "#bdb2ff",
  "喜爱": "#ffb3d9",
  "生气": "#ff9999",
  "思考": "#c2f0c2",
  "困倦": "#d4c5f9"
};

// 自定义Tooltip组件
const CustomTooltip = ({ active, payload, label }) => {
  if (active && payload && payload.length) {
    return (
      <div style={{
        background: 'rgba(255, 255, 255, 0.95)',
        padding: '12px',
        borderRadius: '8px',
        border: '1px solid #ffd6e0',
        boxShadow: '0 4px 12px rgba(0,0,0,0.1)'
      }}>
        <p style={{ margin: 0, color: '#333', fontWeight: 'bold' }}>
          {`${label}: ${payload[0].value} 次`}
        </p>
      </div>
    );
  }
  return null;
};

export default function MoodChart({ data }) {
  return (
    <div style={{
      width: "100%",
      height: 250,
      background: 'rgba(255, 255, 255, 0.3)',
      borderRadius: '16px',
      padding: '20px',
      margin: '20px 0',
      backdropFilter: 'blur(10px)'
    }}>
      <h3 style={{
        textAlign: 'center',
        color: '#ffb703',
        marginBottom: '20px',
        fontSize: '1.3rem'
      }}>
        心情统计 📊
      </h3>
      <ResponsiveContainer>
        <BarChart
          data={data}
          margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
        >
          <XAxis
            dataKey="label"
            axisLine={false}
            tickLine={false}
            tick={{ fontSize: 12, fill: '#666' }}
          />
          <YAxis
            allowDecimals={false}
            axisLine={false}
            tickLine={false}
            tick={{ fontSize: 12, fill: '#666' }}
          />
          <Tooltip content={<CustomTooltip />} />
          <Bar
            dataKey="count"
            radius={[8, 8, 0, 0]}
            animationDuration={1000}
          >
            {data.map((entry, index) => (
              <Cell
                key={`cell-${index}`}
                fill={moodColors[entry.label] || "#ffb703"}
              />
            ))}
          </Bar>
        </BarChart>
      </ResponsiveContainer>
    </div>
  );
}