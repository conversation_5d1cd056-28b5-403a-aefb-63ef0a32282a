import { useState, useEffect } from "react";

const HISTORY_KEY = "mood_history";

export default function MoodHistory({ moods }) {
  const [showHistory, setShowHistory] = useState(false);
  const [history, setHistory] = useState([]);
  const [selectedDate, setSelectedDate] = useState("");

  useEffect(() => {
    loadHistory();
  }, []);

  const loadHistory = () => {
    const saved = localStorage.getItem(HISTORY_KEY);
    if (saved) {
      setHistory(JSON.parse(saved));
    }
  };

  const saveToHistory = (moodKey) => {
    const now = new Date();
    const dateStr = now.toISOString().split('T')[0]; // YYYY-MM-DD
    const timeStr = now.toLocaleTimeString('zh-CN', { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
    
    const mood = moods.find(m => m.key === moodKey);
    if (!mood) return;

    const newEntry = {
      id: Date.now(),
      date: dateStr,
      time: timeStr,
      mood: {
        key: mood.key,
        label: mood.label,
        emoji: mood.emoji,
        color: mood.color
      }
    };

    const updatedHistory = [newEntry, ...history].slice(0, 100); // 保留最近100条
    setHistory(updatedHistory);
    localStorage.setItem(HISTORY_KEY, JSON.stringify(updatedHistory));
  };

  // 暴露保存方法给父组件使用
  useEffect(() => {
    window.saveMoodToHistory = saveToHistory;
  }, [history, moods]);

  const groupedHistory = history.reduce((groups, entry) => {
    const date = entry.date;
    if (!groups[date]) {
      groups[date] = [];
    }
    groups[date].push(entry);
    return groups;
  }, {});

  const formatDate = (dateStr) => {
    const date = new Date(dateStr);
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    if (dateStr === today.toISOString().split('T')[0]) {
      return "今天";
    } else if (dateStr === yesterday.toISOString().split('T')[0]) {
      return "昨天";
    } else {
      return date.toLocaleDateString('zh-CN', { 
        month: 'long', 
        day: 'numeric',
        weekday: 'short'
      });
    }
  };

  const getDayMoodSummary = (dayEntries) => {
    const moodCounts = {};
    dayEntries.forEach(entry => {
      const key = entry.mood.key;
      moodCounts[key] = (moodCounts[key] || 0) + 1;
    });

    const topMood = Object.entries(moodCounts)
      .sort(([,a], [,b]) => b - a)[0];
    
    if (topMood) {
      const mood = moods.find(m => m.key === topMood[0]);
      return {
        emoji: mood?.emoji || "😊",
        label: mood?.label || "未知",
        count: topMood[1]
      };
    }
    return null;
  };

  const filteredHistory = selectedDate 
    ? { [selectedDate]: groupedHistory[selectedDate] || [] }
    : groupedHistory;

  const availableDates = Object.keys(groupedHistory).sort().reverse();

  return (
    <>
      <button 
        className="history-btn"
        onClick={() => setShowHistory(true)}
      >
        心情历史 📅
      </button>

      {showHistory && (
        <div className="history-modal">
          <div className="history-panel">
            <div className="history-header">
              <h3>心情历史</h3>
              <button 
                className="close-btn"
                onClick={() => setShowHistory(false)}
              >
                ✕
              </button>
            </div>

            <div className="history-filters">
              <select 
                value={selectedDate}
                onChange={(e) => setSelectedDate(e.target.value)}
                className="date-filter"
              >
                <option value="">所有日期</option>
                {availableDates.map(date => (
                  <option key={date} value={date}>
                    {formatDate(date)}
                  </option>
                ))}
              </select>
            </div>

            <div className="history-content">
              {Object.keys(filteredHistory).length === 0 ? (
                <div className="empty-history">
                  <p>还没有心情记录哦～</p>
                  <p>快去点击心情按钮开始记录吧！ 🌟</p>
                </div>
              ) : (
                Object.entries(filteredHistory)
                  .sort(([a], [b]) => b.localeCompare(a))
                  .map(([date, dayEntries]) => {
                    const summary = getDayMoodSummary(dayEntries);
                    return (
                      <div key={date} className="history-day">
                        <div className="day-header">
                          <span className="day-date">{formatDate(date)}</span>
                          {summary && (
                            <span className="day-summary">
                              {summary.emoji} {summary.label} 
                              {summary.count > 1 && ` ×${summary.count}`}
                            </span>
                          )}
                        </div>
                        
                        <div className="day-entries">
                          {dayEntries.map(entry => (
                            <div key={entry.id} className="history-entry">
                              <span className="entry-time">{entry.time}</span>
                              <span 
                                className="entry-mood"
                                style={{ background: entry.mood.color }}
                              >
                                {entry.mood.emoji} {entry.mood.label}
                              </span>
                            </div>
                          ))}
                        </div>
                      </div>
                    );
                  })
              )}
            </div>
          </div>
        </div>
      )}
    </>
  );
}
