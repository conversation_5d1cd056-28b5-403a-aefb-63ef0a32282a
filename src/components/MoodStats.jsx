import React from "react";

export default function MoodStats({ data }) {
  const totalClicks = data.reduce((sum, mood) => sum + mood.count, 0);
  
  // 找出最常见的心情
  const mostCommonMood = data.reduce((max, mood) => 
    mood.count > max.count ? mood : max, 
    { count: 0, label: "暂无" }
  );
  
  // 计算心情分布百分比
  const moodPercentages = data.map(mood => ({
    ...mood,
    percentage: totalClicks > 0 ? ((mood.count / totalClicks) * 100).toFixed(1) : 0
  }));

  // 心情状态分析
  const getMoodAnalysis = () => {
    if (totalClicks === 0) return "还没有记录心情哦，快来点击按钮记录你的心情吧！ 🌟";
    
    const happyMoods = ["开心", "超开心", "喜爱"];
    const sadMoods = ["不开心", "生气"];
    const neutralMoods = ["平静", "思考", "困倦"];
    
    const happyCount = data.filter(m => happyMoods.includes(m.label)).reduce((sum, m) => sum + m.count, 0);
    const sadCount = data.filter(m => sadMoods.includes(m.label)).reduce((sum, m) => sum + m.count, 0);
    const neutralCount = data.filter(m => neutralMoods.includes(m.label)).reduce((sum, m) => sum + m.count, 0);
    
    const happyRatio = (happyCount / totalClicks) * 100;
    const sadRatio = (sadCount / totalClicks) * 100;
    
    if (happyRatio > 60) {
      return "你最近的心情很棒呢！满满的正能量 ✨";
    } else if (sadRatio > 40) {
      return "最近可能有些小烦恼，记得要好好照顾自己哦 🤗";
    } else if (neutralCount > totalClicks * 0.5) {
      return "你是一个很平和的人呢，保持这份内心的宁静 🧘‍♀️";
    } else {
      return "你的心情很丰富多彩，这就是生活的美好 🌈";
    }
  };

  return (
    <div className="mood-stats">
      <h3 className="stats-title">心情分析 📈</h3>
      
      <div className="stats-grid">
        <div className="stat-card">
          <div className="stat-number">{totalClicks}</div>
          <div className="stat-label">总记录次数</div>
        </div>
        
        <div className="stat-card">
          <div className="stat-emoji">{mostCommonMood.count > 0 ? 
            data.find(m => m.label === mostCommonMood.label)?.emoji || "😊" : "😊"
          }</div>
          <div className="stat-label">最常见心情</div>
          <div className="stat-sublabel">{mostCommonMood.label}</div>
        </div>
      </div>

      <div className="mood-analysis">
        <p className="analysis-text">{getMoodAnalysis()}</p>
      </div>

      {totalClicks > 0 && (
        <div className="mood-breakdown">
          <h4>心情分布</h4>
          {moodPercentages
            .filter(mood => mood.count > 0)
            .sort((a, b) => b.count - a.count)
            .map(mood => (
              <div key={mood.label} className="breakdown-item">
                <span className="breakdown-emoji">
                  {data.find(m => m.label === mood.label)?.emoji}
                </span>
                <span className="breakdown-label">{mood.label}</span>
                <span className="breakdown-count">{mood.count}次</span>
                <span className="breakdown-percentage">({mood.percentage}%)</span>
              </div>
            ))
          }
        </div>
      )}
    </div>
  );
}
