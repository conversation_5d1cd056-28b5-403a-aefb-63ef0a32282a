import React, { useEffect, useState } from "react";

export default function FloatingEmoji({ emoji, trigger }) {
  const [show, setShow] = useState(false);

  useEffect(() => {
    if (trigger) {
      setShow(true);
      const timer = setTimeout(() => setShow(false), 1200);
      return () => clearTimeout(timer);
    }
  }, [trigger]);

  if (!show) return null;

  return (
    <div className="floating-emoji">{emoji}</div>
  );
} 