import { useEffect, useState } from "react";

export default function FloatingEmoji({ emoji, trigger, x, y }) {
  const [show, setShow] = useState(false);

  useEffect(() => {
    if (trigger) {
      setShow(true);
      const timer = setTimeout(() => setShow(false), 1200);
      return () => clearTimeout(timer);
    }
  }, [trigger]);

  if (!show) return null;

  // 根据屏幕大小动态调整字体大小
  const getFontSize = () => {
    if (window.innerWidth <= 480) return '2rem';
    if (window.innerWidth <= 768) return '2.5rem';
    return '3.5rem';
  };

  return (
    <div
      className="floating-emoji"
      style={{
        position: 'fixed',
        left: x ? `${x}px` : '50%',
        top: y ? `${y}px` : '60%',
        transform: x && y ? 'translate(-50%, -50%)' : 'translate(-50%, 0)',
        fontSize: getFontSize(),
        pointerEvents: 'none',
        animation: 'floatUp 1.2s cubic-bezier(.4,1.6,.6,1) forwards',
        zIndex: 100
      }}
    >
      {emoji}
    </div>
  );
}