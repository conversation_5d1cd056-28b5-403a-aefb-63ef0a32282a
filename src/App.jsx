import { useEffect, useRef, useState } from "react";
import FloatingEmoji from "./components/FloatingEmoji";
import MoodButton from "./components/MoodButton";
import MoodChart from "./components/MoodChart";
import { moods } from "./data/moods";
import "./styles/app.css";

const STORAGE_KEY = "mood_counts";
const SAVE_INTERVAL = 5 * 60 * 1000; // 5分钟

function getInitialCounts() {
  const saved = localStorage.getItem(STORAGE_KEY);
  if (saved) return JSON.parse(saved);
  return moods.reduce((acc, m) => ({ ...acc, [m.key]: 0 }), {});
}

export default function App() {
  const [counts, setCounts] = useState(getInitialCounts);
  const [bg, setBg] = useState(moods[0].bg);
  const [float, setFloat] = useState({ emoji: "", trigger: 0 });
  const [volume, setVolume] = useState(0.5);
  const [soundEnabled, setSoundEnabled] = useState(true);
  const audioRef = useRef({});

  // 初始化音效
  useEffect(() => {
    moods.forEach(m => {
      const audio = new Audio(m.sound);
      audio.volume = volume;
      audio.preload = 'auto';

      // 处理音频加载错误
      audio.onerror = () => {
        console.warn(`音效文件加载失败: ${m.sound}`);
      };

      audioRef.current[m.key] = audio;
    });
  }, []);

  // 更新音量
  useEffect(() => {
    Object.values(audioRef.current).forEach(audio => {
      if (audio) audio.volume = volume;
    });
  }, [volume]);

  // 自动保存
  useEffect(() => {
    const interval = setInterval(() => {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(counts));
    }, SAVE_INTERVAL);
    return () => clearInterval(interval);
  }, [counts]);

  // 每次点击都保存
  useEffect(() => {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(counts));
  }, [counts]);

  const handleMoodClick = mood => {
    setCounts(c => ({ ...c, [mood.key]: c[mood.key] + 1 }));
    setBg(mood.bg);
    setFloat({ emoji: mood.emoji, trigger: Date.now() });

    // 播放音效
    if (soundEnabled) {
      const audio = audioRef.current[mood.key];
      if (audio) {
        audio.currentTime = 0;
        audio.play().catch(err => {
          console.warn('音效播放失败:', err);
        });
      }
    }
  };

  const chartData = moods.map(m => ({
    label: m.label,
    count: counts[m.key] || 0
  }));

  return (
    <div className="app" style={{ background: bg }}>
      <h1 className="title">可爱的心情站</h1>

      {/* 音效控制 */}
      <div className="sound-controls">
        <button
          className="sound-toggle"
          onClick={() => setSoundEnabled(!soundEnabled)}
          title={soundEnabled ? "关闭音效" : "开启音效"}
        >
          {soundEnabled ? "🔊" : "🔇"}
        </button>
        <input
          type="range"
          min="0"
          max="1"
          step="0.1"
          value={volume}
          onChange={(e) => setVolume(parseFloat(e.target.value))}
          className="volume-slider"
          title="音量控制"
        />
      </div>

      <div className="mood-btns">
        {moods.map(mood => (
          <MoodButton
            key={mood.key}
            mood={mood}
            count={counts[mood.key] || 0}
            onClick={handleMoodClick}
          />
        ))}
      </div>
      <FloatingEmoji emoji={float.emoji} trigger={float.trigger} />
      <MoodChart data={chartData} />
      <footer className="footer">治愈每一天 💖</footer>
    </div>
  );
}