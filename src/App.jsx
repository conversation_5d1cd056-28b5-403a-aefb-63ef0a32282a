import { useEffect, useRef, useState } from "react";
import FloatingEmoji from "./components/FloatingEmoji";
import MoodButton from "./components/MoodButton";
import MoodChart from "./components/MoodChart";
import MoodHistory from "./components/MoodHistory";
import MoodStats from "./components/MoodStats";
import ShareCard from "./components/ShareCard";
import { moods } from "./data/moods";
import "./styles/app.css";

const STORAGE_KEY = "mood_counts";
const SAVE_INTERVAL = 5 * 60 * 1000; // 5分钟

function getInitialCounts() {
  const saved = localStorage.getItem(STORAGE_KEY);
  if (saved) return JSON.parse(saved);
  return moods.reduce((acc, m) => ({ ...acc, [m.key]: 0 }), {});
}

export default function App() {
  const [counts, setCounts] = useState(getInitialCounts);
  const [bg, setBg] = useState(moods[0].bg);
  const [float, setFloat] = useState({ emoji: "", trigger: 0 });
  const [volume, setVolume] = useState(0.5);
  const [soundEnabled, setSoundEnabled] = useState(true);
  const audioRef = useRef({});

  // 初始化音效
  useEffect(() => {
    moods.forEach(m => {
      const audio = new Audio(m.sound);
      audio.volume = volume;
      audio.preload = 'auto';

      // 处理音频加载错误
      audio.onerror = () => {
        console.warn(`音效文件加载失败: ${m.sound}`);
      };

      audioRef.current[m.key] = audio;
    });
  }, []);

  // 更新音量
  useEffect(() => {
    Object.values(audioRef.current).forEach(audio => {
      if (audio) audio.volume = volume;
    });
  }, [volume]);

  // 自动保存
  useEffect(() => {
    const interval = setInterval(() => {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(counts));
    }, SAVE_INTERVAL);
    return () => clearInterval(interval);
  }, [counts]);

  // 每次点击都保存
  useEffect(() => {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(counts));
  }, [counts]);

  const handleMoodClick = mood => {
    setCounts(c => ({ ...c, [mood.key]: c[mood.key] + 1 }));
    setBg(mood.bg);
    setFloat({ emoji: mood.emoji, trigger: Date.now() });

    // 保存到历史记录
    if (window.saveMoodToHistory) {
      window.saveMoodToHistory(mood.key);
    }

    // 播放音效
    if (soundEnabled) {
      const audio = audioRef.current[mood.key];
      if (audio) {
        audio.currentTime = 0;
        audio.play().catch(err => {
          console.warn('音效播放失败:', err);
        });
      }
    }
  };

  const chartData = moods.map(m => ({
    label: m.label,
    count: counts[m.key] || 0
  }));

  return (
    <div className="app" style={{ background: bg }}>
      <header>
        <h1 className="title">心泡泡</h1>
        <p className="subtitle" style={{
          textAlign: 'center',
          color: '#666',
          fontSize: '1rem',
          margin: '0 0 20px 0',
          display: 'none' // 隐藏但对SEO有用
        }}>
          记录每日心情，分享美好生活 - 治愈系心情记录与分析平台
        </p>
      </header>

      {/* 音效控制 */}
      <section className="sound-controls" aria-label="音效控制">
        <button
          className="sound-toggle"
          onClick={() => setSoundEnabled(!soundEnabled)}
          title={soundEnabled ? "关闭音效" : "开启音效"}
          aria-label={soundEnabled ? "关闭音效" : "开启音效"}
        >
          {soundEnabled ? "🔊" : "🔇"}
        </button>
        <input
          type="range"
          min="0"
          max="1"
          step="0.1"
          value={volume}
          onChange={(e) => setVolume(parseFloat(e.target.value))}
          className="volume-slider"
          title="音量控制"
          aria-label="音量控制"
        />
      </section>

      <main>
        <section className="mood-btns" aria-label="心情选择">
          <h2 style={{ display: 'none' }}>选择你的心情</h2>
          {moods.map(mood => (
            <MoodButton
              key={mood.key}
              mood={mood}
              count={counts[mood.key] || 0}
              onClick={handleMoodClick}
            />
          ))}
        </section>
        <FloatingEmoji emoji={float.emoji} trigger={float.trigger} />

        <section aria-label="心情统计">
          <MoodStats data={chartData} />
        </section>

        <section aria-label="心情图表">
          <MoodChart data={chartData} />
        </section>

        <section aria-label="功能操作" className="action-buttons">
          <ShareCard data={chartData} />
          <MoodHistory moods={moods} />
        </section>
      </main>

      <footer className="footer">治愈每一天 💖</footer>
    </div>
  );
}