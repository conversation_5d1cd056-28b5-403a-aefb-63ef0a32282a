import React, { useState, useEffect, useRef } from "react";
import { moods } from "./data/moods";
import MoodButton from "./components/MoodButton";
import FloatingEmoji from "./components/FloatingEmoji";
import Mood<PERSON>hart from "./components/MoodChart";
import "./styles/app.css";

const STORAGE_KEY = "mood_counts";
const SAVE_INTERVAL = 5 * 60 * 1000; // 5分钟

function getInitialCounts() {
  const saved = localStorage.getItem(STORAGE_KEY);
  if (saved) return JSON.parse(saved);
  return moods.reduce((acc, m) => ({ ...acc, [m.key]: 0 }), {});
}

export default function App() {
  const [counts, setCounts] = useState(getInitialCounts);
  const [bg, setBg] = useState(moods[0].bg);
  const [float, setFloat] = useState({ emoji: "", trigger: 0 });
  const audioRef = useRef({});

  // 初始化音效
  useEffect(() => {
    moods.forEach(m => {
      audioRef.current[m.key] = new Audio(m.sound);
    });
  }, []);

  // 自动保存
  useEffect(() => {
    const interval = setInterval(() => {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(counts));
    }, SAVE_INTERVAL);
    return () => clearInterval(interval);
  }, [counts]);

  // 每次点击都保存
  useEffect(() => {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(counts));
  }, [counts]);

  const handleMoodClick = mood => {
    setCounts(c => ({ ...c, [mood.key]: c[mood.key] + 1 }));
    setBg(mood.bg);
    setFloat({ emoji: mood.emoji, trigger: Date.now() });
    const audio = audioRef.current[mood.key];
    if (audio) {
      audio.currentTime = 0;
      audio.play();
    }
  };

  const chartData = moods.map(m => ({
    label: m.label,
    count: counts[m.key] || 0
  }));

  return (
    <div className="app" style={{ background: bg }}>
      <h1 className="title">可爱的心情站</h1>
      <div className="mood-btns">
        {moods.map(mood => (
          <MoodButton
            key={mood.key}
            mood={mood}
            count={counts[mood.key] || 0}
            onClick={handleMoodClick}
          />
        ))}
      </div>
      <FloatingEmoji emoji={float.emoji} trigger={float.trigger} />
      <MoodChart data={chartData} />
      <footer className="footer">治愈每一天 💖</footer>
    </div>
  );
} 