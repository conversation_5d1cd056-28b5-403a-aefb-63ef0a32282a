<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />

    <!-- 基本SEO信息 -->
    <title>心泡泡 - 治愈系心情记录与分析平台 | 记录每日心情，分享美好生活</title>
    <meta name="description" content="心泡泡是一个治愈系的心情记录平台，帮助你记录每日心情，查看心情统计，分享心情状态。支持8种心情类型（开心、不开心、超开心、平静、喜爱、生气、思考、困倦），提供美观的数据可视化和历史记录功能。匿名使用，数据本地保存，保护隐私安全。" />
    <meta name="keywords" content="心情记录,心情日记,情绪管理,心理健康,治愈系,心情分析,心情统计,心情分享,情绪追踪,心情app,心理工具,情感记录,心情可视化,心情历史" />
    <meta name="author" content="心泡泡" />
    <meta name="robots" content="index, follow" />
    <meta name="language" content="zh-CN" />
    <meta name="revisit-after" content="7 days" />
    <meta name="rating" content="general" />
    <meta name="distribution" content="global" />
    <meta name="copyright" content="心泡泡" />

    <!-- 移动端优化 -->
    <meta name="format-detection" content="telephone=no" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="default" />
    <meta name="apple-mobile-web-app-title" content="心情站" />

    <!-- 图标 -->
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <link rel="icon" type="image/x-icon" href="/favicon.ico" />
    <link rel="apple-touch-icon" href="/favicon.svg" />
    <link rel="manifest" href="/manifest.json" />

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://mood.aolifu.org/" />
    <meta property="og:title" content="心泡泡 - 治愈系心情记录平台" />
    <meta property="og:description" content="记录每日心情，查看心情统计，分享心情状态。支持8种心情类型的治愈系心情记录平台。" />
    <meta property="og:image" content="https://mood.aolifu.org/favicon.svg" />
    <meta property="og:locale" content="zh_CN" />

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:url" content="https://mood.aolifu.org/" />
    <meta property="twitter:title" content="心泡泡 - 治愈系心情记录平台" />
    <meta property="twitter:description" content="记录每日心情，查看心情统计，分享心情状态。支持8种心情类型的治愈系心情记录平台。" />
    <meta property="twitter:image" content="https://mood.aolifu.org/favicon.svg" />

    <!-- 主题颜色 -->
    <meta name="theme-color" content="#ffb703" />
    <meta name="msapplication-TileColor" content="#ffb703" />

    <!-- 性能优化 -->
    <link rel="preload" href="/src/main.jsx" as="script" />
    <link rel="preload" href="/src/styles/app.css" as="style" />
    <link rel="dns-prefetch" href="//fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.googleapis.com" crossorigin />

    <!-- 资源提示 -->
    <meta http-equiv="x-dns-prefetch-control" content="on" />
    <meta name="referrer" content="no-referrer-when-downgrade" />

    <!-- 结构化数据 -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "WebApplication",
      "name": "心泡泡",
      "alternateName": "心情站",
      "description": "治愈系心情记录与分析平台，帮助用户记录每日心情，查看心情统计，分享心情状态",
      "url": "https://mood.aolifu.org",
      "applicationCategory": "LifestyleApplication",
      "operatingSystem": "Web",
      "offers": {
        "@type": "Offer",
        "price": "0",
        "priceCurrency": "CNY"
      },
      "author": {
        "@type": "Organization",
        "name": "心泡泡团队"
      },
      "featureList": [
        "心情记录",
        "心情统计分析",
        "心情历史查看",
        "心情分享",
        "数据可视化",
        "音效反馈",
        "响应式设计"
      ],
      "screenshot": "https://mood.aolifu.org/favicon.svg",
      "softwareVersion": "1.0.0",
      "datePublished": "2024-01-01",
      "inLanguage": "zh-CN"
    }
    </script>
    <script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-3811349067654166"
            crossorigin="anonymous"></script>
    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-B21BGZY27D"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());

      gtag('config', 'G-B21BGZY27D');
    </script>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.jsx"></script>
  </body>
</html>