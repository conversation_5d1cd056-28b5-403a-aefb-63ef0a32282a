<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100">
  <defs>
    <linearGradient id="bg" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#fffbe6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#fff0f6;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="heart" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffb703;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ffd60a;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景圆形 -->
  <circle cx="50" cy="50" r="48" fill="url(#bg)" stroke="#ffb703" stroke-width="2"/>
  
  <!-- 心形图标 -->
  <path d="M50 75 C40 65, 25 50, 25 35 C25 25, 35 20, 45 25 C47 27, 50 30, 50 30 C50 30, 53 27, 55 25 C65 20, 75 25, 75 35 C75 50, 60 65, 50 75 Z" fill="url(#heart)"/>
  
  <!-- 可爱的小星星装饰 -->
  <circle cx="25" cy="25" r="2" fill="#ffd60a" opacity="0.8"/>
  <circle cx="75" cy="25" r="2" fill="#ffd60a" opacity="0.8"/>
  <circle cx="25" cy="75" r="2" fill="#ffd60a" opacity="0.8"/>
  <circle cx="75" cy="75" r="2" fill="#ffd60a" opacity="0.8"/>
</svg>
